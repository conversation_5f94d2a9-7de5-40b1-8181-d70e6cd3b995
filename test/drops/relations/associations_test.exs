defmodule Drops.Relations.AssociationsTest do
  use Drops.OperationCase, async: true

  describe "associations" do
    @tag ecto_schemas: [Test.Ecto.UserGroupSchemas.User, Test.Ecto.UserGroupSchemas.Group]
    test "sets up regular Ecto associations" do
      defmodule Test.Users do
        use Drops.Relation, repo: Drops.TestRepo, name: "users", infer: true

        associations do
          many_to_many(:groups, Test.Ecto.UserGroupSchemas.Group,
            join_through: "user_groups"
          )
        end
      end

      defmodule Test.Groups do
        use Drops.Relation, repo: Drops.TestRepo, name: "groups", infer: true

        associations do
          many_to_many(:users, Test.Ecto.UserGroupSchemas.User,
            join_through: "user_groups"
          )
        end
      end

      # Check that associations are properly set up in the Ecto schema
      # Since associations are handled by Ecto directly, check the __schema__ function
      associations = Test.Users.Schema.__schema__(:associations)
      assert length(associations) > 0
      assert :groups in associations

      # Check the association type through Ecto's __schema__ function
      assert Test.Users.Schema.__schema__(:association, :groups).__struct__ ==
               Ecto.Association.ManyToMany
    end
  end
end
